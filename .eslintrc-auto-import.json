{"globals": {"$http": true, "BSCustomApprovalForm": true, "BSDebugPage": true, "BSFileTypeTree": true, "BSFormText": true, "BSGrantRecycle": true, "BSImage": true, "BSInclusionProcess": true, "BSJsonEditor": true, "BSMinioUpload": true, "BSNoticeModel": true, "BSPaperDisposalForm": true, "BSPeopleSelect": true, "BSProcessLackDepartment": true, "BSProcessLackPersonnel": true, "BSRadio": true, "BSRecycleForm": true, "BSSelectTreeDictionary": true, "BSSelectTreeOrganization": true, "BSSignSet": true, "BSSignatureImage": true, "BSTab": true, "BSTimeline": true, "BSTodoNoticeCardItem": true, "BSTodoNoticeList": true, "BSTypeWriter": true, "BSTypeWriterDemo": true, "BsBorrowForm": true, "BsCustomApprovalForm": true, "BsDebugPage": true, "BsFileTypeTree": true, "BsFormText": true, "BsGrantRecycle": true, "BsImage": true, "BsInclusionProcess": true, "BsJsonEditor": true, "BsMinioUpload": true, "BsNoticeModel": true, "BsPaperDisposalForm": true, "BsPeopleSelect": true, "BsProcessLackDepartment": true, "BsProcessLackPersonnel": true, "BsRadio": true, "BsRecycleForm": true, "BsSelectTreeDictionary": true, "BsSelectTreeOrganization": true, "BsSignSet": true, "BsSignatureImage": true, "BsTab": true, "BsTimeline": true, "BsTodoNoticeCardItem": true, "BsTodoNoticeList": true, "BsTypeWriter": true, "BsTypeWriterDemo": true, "Component": true, "ComponentPublicInstance": true, "ComputedRef": true, "CustomApprovalForm": true, "DebugPage": true, "DirectiveBinding": true, "EffectScope": true, "ExtractDefaultPropTypes": true, "ExtractPropTypes": true, "ExtractPublicPropTypes": true, "FileTypeTree": true, "FormText": true, "GrantRecycle": true, "Image": true, "InclusionProcess": true, "IndexHooks": true, "InjectionKey": true, "JsonEditor": true, "MaybeRef": true, "MaybeRefOrGetter": true, "MinioUpload": true, "NoticeModel": true, "PaperDisposalForm": true, "PeopleSelect": true, "ProcessLackDepartment": true, "ProcessLackPersonnel": true, "PropType": true, "Radio": true, "RecycleForm": true, "Ref": true, "RequestPromise": true, "SelectTreeDictionary": true, "SelectTreeOrganization": true, "SignSet": true, "SignatureImage": true, "Tab": true, "Timeline": true, "TodoNoticeCardItem": true, "TodoNoticeList": true, "TypeWriter": true, "TypeWriterDemo": true, "UseApprovalProcessHooks": true, "UseSseHooks": true, "VNode": true, "WritableComputedRef": true, "acceptHMRUpdate": true, "api": true, "asyncComputed": true, "autoResetRef": true, "bsCustomApprovalForm": true, "bsDebugPage": true, "bsFileTypeTree": true, "bsFormText": true, "bsGrantRecycle": true, "bsImage": true, "bsInclusionProcess": true, "bsJsonEditor": true, "bsMinioUpload": true, "bsNoticeModel": true, "bsPaperDisposalForm": true, "bsPeopleSelect": true, "bsProcessLackDepartment": true, "bsProcessLackPersonnel": true, "bsRadio": true, "bsRecycleForm": true, "bsSelectTreeDictionary": true, "bsSelectTreeOrganization": true, "bsSignSet": true, "bsSignatureImage": true, "bsTab": true, "bsTimeline": true, "bsTodoNoticeCardItem": true, "bsTodoNoticeList": true, "bsTypeWriter": true, "bsTypeWriterDemo": true, "cancelAll": true, "computed": true, "computedAsync": true, "computedEager": true, "computedInject": true, "computedWithControl": true, "controlledComputed": true, "controlledRef": true, "createApp": true, "createEventHook": true, "createGlobalState": true, "createInjectionState": true, "createPinia": true, "createReactiveFn": true, "createSharedComposable": true, "createUnrefFn": true, "customApprovalForm": true, "customRef": true, "dataURIToBlob": true, "debouncedRef": true, "debouncedWatch": true, "debugPage": true, "defineAsyncComponent": true, "defineComponent": true, "defineStore": true, "download": true, "eagerComputed": true, "effectScope": true, "extendRef": true, "fh": true, "fileTypeTree": true, "formText": true, "fz": true, "getActivePinia": true, "getCurrentInstance": true, "getCurrentScope": true, "grantRecycle": true, "h": true, "ignorableWatch": true, "image": true, "inclusionProcess": true, "indexHooks": true, "inject": true, "isDefined": true, "isProxy": true, "isReactive": true, "isReadonly": true, "isRef": true, "jsonEditor": true, "makeDestructurable": true, "mapActions": true, "mapGetters": true, "mapState": true, "mapStores": true, "mapWritableState": true, "markRaw": true, "minioUpload": true, "nextTick": true, "noticeModel": true, "onActivated": true, "onBeforeMount": true, "onBeforeRouteLeave": true, "onBeforeRouteUpdate": true, "onBeforeUnmount": true, "onBeforeUpdate": true, "onClickOutside": true, "onDeactivated": true, "onErrorCaptured": true, "onKeyStroke": true, "onLongPress": true, "onMounted": true, "onRenderTracked": true, "onRenderTriggered": true, "onScopeDispose": true, "onServerPrefetch": true, "onStartTyping": true, "onUnmounted": true, "onUpdated": true, "onWatcherCleanup": true, "paperDisposalForm": true, "pausableWatch": true, "peopleSelect": true, "preprocessor": true, "processLackDepartment": true, "processLackPersonnel": true, "provide": true, "radio": true, "reactify": true, "reactifyObject": true, "reactive": true, "reactiveComputed": true, "reactiveOmit": true, "reactivePick": true, "readonly": true, "recycleForm": true, "ref": true, "refAutoReset": true, "refDebounced": true, "refDefault": true, "refThrottled": true, "refWithControl": true, "request": true, "requestAll": true, "resolveComponent": true, "resolveRef": true, "resolveUnref": true, "selectTreeDictionary": true, "selectTreeOrganization": true, "setActivePinia": true, "setMapStoreSuffix": true, "shallowReactive": true, "shallowReadonly": true, "shallowRef": true, "signSet": true, "signatureImage": true, "storeToRefs": true, "syncRef": true, "syncRefs": true, "tab": true, "templateRef": true, "throttledRef": true, "throttledWatch": true, "timeline": true, "toHex": true, "toRaw": true, "toReactive": true, "toRef": true, "toRefs": true, "toValue": true, "todoNoticeCardItem": true, "todoNoticeList": true, "transParams": true, "triggerRef": true, "tryOnBeforeMount": true, "tryOnBeforeUnmount": true, "tryOnMounted": true, "tryOnScopeDispose": true, "tryOnUnmounted": true, "typeWriter": true, "typeWriterDemo": true, "unref": true, "unrefElement": true, "until": true, "useActiveElement": true, "useApprovalProcessHooks": true, "useArrayEvery": true, "useArrayFilter": true, "useArrayFind": true, "useArrayFindIndex": true, "useArrayFindLast": true, "useArrayJoin": true, "useArrayMap": true, "useArrayReduce": true, "useArraySome": true, "useArrayUnique": true, "useAsyncQueue": true, "useAsyncState": true, "useAttrs": true, "useBase64": true, "useBattery": true, "useBluetooth": true, "useBreakpoints": true, "useBroadcastChannel": true, "useBrowserLocation": true, "useCached": true, "useClipboard": true, "useCloned": true, "useColorMode": true, "useConfirmDialog": true, "useCounter": true, "useCssModule": true, "useCssVar": true, "useCssVars": true, "useCurrentElement": true, "useCycleList": true, "useDark": true, "useDateFormat": true, "useDebounce": true, "useDebounceFn": true, "useDebouncedRefHistory": true, "useDeviceMotion": true, "useDeviceOrientation": true, "useDevicePixelRatio": true, "useDevicesList": true, "useDisplayMedia": true, "useDocumentVisibility": true, "useDraggable": true, "useDropZone": true, "useElementBounding": true, "useElementByPoint": true, "useElementHover": true, "useElementSize": true, "useElementVisibility": true, "useEventBus": true, "useEventListener": true, "useEventSource": true, "useEyeDropper": true, "useFavicon": true, "useFetch": true, "useFileDialog": true, "useFileSystemAccess": true, "useFocus": true, "useFocusWithin": true, "useFps": true, "useFullscreen": true, "useGamepad": true, "useGeolocation": true, "useId": true, "useIdle": true, "useImage": true, "useInfiniteScroll": true, "useIntersectionObserver": true, "useInterval": true, "useIntervalFn": true, "useKeyModifier": true, "useLastChanged": true, "useLink": true, "useLocalStorage": true, "useMagicKeys": true, "useManualRefHistory": true, "useMediaControls": true, "useMediaQuery": true, "useMemoize": true, "useMemory": true, "useModel": true, "useMounted": true, "useMouse": true, "useMouseInElement": true, "useMousePressed": true, "useMutationObserver": true, "useNavigatorLanguage": true, "useNetwork": true, "useNow": true, "useObjectUrl": true, "useOffsetPagination": true, "useOnline": true, "usePageLeave": true, "useParallax": true, "usePermission": true, "usePointer": true, "usePointerLock": true, "usePointerSwipe": true, "usePreferredColorScheme": true, "usePreferredContrast": true, "usePreferredDark": true, "usePreferredLanguages": true, "usePreferredReducedMotion": true, "usePrevious": true, "useRafFn": true, "useRefHistory": true, "useResizeObserver": true, "useRoute": true, "useRouter": true, "useScreenOrientation": true, "useScreenSafeArea": true, "useScriptTag": true, "useScroll": true, "useScrollLock": true, "useSessionStorage": true, "useShare": true, "useSlots": true, "useSorted": true, "useSpeechRecognition": true, "useSpeechSynthesis": true, "useSseHooks": true, "useStepper": true, "useStorage": true, "useStorageAsync": true, "useStyleTag": true, "useSupported": true, "useSwipe": true, "useTemplateRef": true, "useTemplateRefsList": true, "useTextDirection": true, "useTextSelection": true, "useTextareaAutosize": true, "useThrottle": true, "useThrottleFn": true, "useThrottledRefHistory": true, "useTimeAgo": true, "useTimeout": true, "useTimeoutFn": true, "useTimeoutPoll": true, "useTimestamp": true, "useTitle": true, "useToNumber": true, "useToString": true, "useToggle": true, "useTransition": true, "useUrlSearchParams": true, "useUserMedia": true, "useVModel": true, "useVModels": true, "useVibrate": true, "useVirtualList": true, "useWakeLock": true, "useWebNotification": true, "useWebSocket": true, "useWebWorker": true, "useWebWorkerFn": true, "useWindowFocus": true, "useWindowScroll": true, "useWindowSize": true, "watch": true, "watchArray": true, "watchAtMost": true, "watchDebounced": true, "watchEffect": true, "watchIgnorable": true, "watchOnce": true, "watchPausable": true, "watchPostEffect": true, "watchSyncEffect": true, "watchThrottled": true, "watchTriggerable": true, "watchWithFilter": true, "whenever": true}}