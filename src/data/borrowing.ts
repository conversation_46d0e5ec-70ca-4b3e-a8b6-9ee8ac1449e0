
interface StatusOption {
    label: string;
    value: number;
    type: 'default' | 'primary' | 'success' | 'error';
}
/**
 * 借阅与交还 状态
 */
const status: StatusOption[] = [
    {
        label: '系统处理中',
        value: -1,
        type: 'default'
    },
    {
        label: '待提交',
        value: 1,
        type: 'default'
    },
    {
        label: '待审批',
        value: 2,
        type: 'primary'
    },
    {
        label: '已审批',
        value: 3,
        type: 'success'
    },
    {
        label: '已驳回',
        value: 4,
        type: 'error'
    }
];

export default {
    status
};
