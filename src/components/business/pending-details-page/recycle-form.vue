<template>
    <div class="recycle-form">
        <n-form class="useFormDialog" label-placement="left" :show-feedback="false" :label-width="110">
            <n-grid :cols="24" :x-gap="10" :y-gap="10">
                <n-form-item-gi label="回收人" :span="12">
                    <n-input :value="modelValue?.recycler?.nickname || ''" readonly />
                </n-form-item-gi>
                <n-form-item-gi label="回收日期" :span="12">
                    <n-input :value="modelValue?.recycler?.recycleDate || ''" readonly placeholder="" />
                </n-form-item-gi>
                <n-form-item-gi label="发放类型" :span="12">
                    <n-input :value="modelValue?.distributeData?.distributeTypeText || ''" readonly placeholder="" />
                </n-form-item-gi>
                <n-form-item-gi label="文件类型" :span="12">
                    <n-input :value="modelValue?.distributeData?.fileTypeText || ''" readonly placeholder="" />
                </n-form-item-gi>
                <n-form-item-gi label="文件类别" :span="12">
                    <n-input :value="modelValue?.distributeData?.fileCategory || ''" readonly placeholder="" />
                </n-form-item-gi>
                <n-form-item-gi label="发放原因" :span="12" v-if="modelValue?.distributeData?.reason">
                    <n-input :value="modelValue?.distributeData?.reason || ''" readonly placeholder="" />
                </n-form-item-gi>
                <n-form-item-gi label="其他原因" :span="24" v-if="modelValue?.distributeData?.otherReason">
                    <n-input :value="modelValue?.distributeData?.otherReason || ''" readonly placeholder="" />
                </n-form-item-gi>
                <n-form-item-gi
                    label="期望发放日期"
                    :span="12"
                    v-if="modelValue?.distributeData?.wishDistributeDateText"
                >
                    <n-input
                        :value="modelValue?.distributeData?.wishDistributeDateText || ''"
                        readonly
                        placeholder=""
                    />
                </n-form-item-gi>
                <n-form-item-gi label="回收原因" :span="12">
                    <n-input :value="modelValue?.recycleReasonInfo?.reason || ''" readonly placeholder="" />
                </n-form-item-gi>
                <n-form-item-gi label="其他原因" :span="24" v-if="modelValue?.recycleReasonInfo?.otherReason">
                    <n-input :value="modelValue?.recycleReasonInfo?.otherReason || ''" readonly placeholder="" />
                </n-form-item-gi>
            </n-grid>

            <div class="mt-16px">
                <n-form-item label="回收清单" class="data-table"> </n-form-item>
                <vxe-table :data="tableData" border>
                    <vxe-column field="fileName" title="文件名称" width="160" />
                    <vxe-column field="number" title="文件编号" width="140" />
                    <vxe-column field="fileForm" title="版本/版次" width="120" />
                    <vxe-column field="internalFileRead" title="内发:电子文件-查阅" width="200" />
                    <vxe-column field="internalFileReadAndDownload" title="内发:电子文件-查阅/下载" width="200" />
                    <vxe-column field="internalFileOneDownload" title="内发:纸质文件-一次下载" width="200" />
                </vxe-table>
            </div>
        </n-form>
    </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { VxeTable, VxeColumn } from 'vxe-table';

const props = withDefaults(
    defineProps<{
        modelValue?: any;
    }>(),
    {
        modelValue: ''
    }
);
const { modelValue } = props;

// 表格数据
const tableData = computed(() => {
    const flowData = props.modelValue;
    const list = Array.isArray(flowData?.recycleListInfo) ? flowData.recycleListInfo : [];
    return list.map((file: any) => {
        return {
            fileName: file.fileName || '',
            number: file.number || file.fileNo || '',
            fileForm: file.fileForm || '',
            internalFileRead: file.internalFileRead.map((item: any) => item.label).join('，'),
            internalFileReadAndDownload: file.internalFileReadAndDownload.map((item: any) => item.label).join('，'),
            internalFileOneDownload: file.internalFileOneDownload.map((item: any) => item.label).join('，')
        };
    });
});
console.log(tableData.value, 'tableData');

console.log(props.modelValue);
</script>

<style scoped lang="less">
.recycle-form {
    width: 100%;
}
.data-table {
    :deep(.n-form-item-blank) {
        display: none;
    }
}
</style>
