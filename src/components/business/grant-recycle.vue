<template>
    <div class="transfer-container">
        <!-- 左侧组织树 -->
        <div class="transfer-panel">
            <div class="panel-header">
                <n-checkbox v-if="!isPaperDownload" :checked="isAllLeftChecked" @update:checked="toggleLeftCheckAll">
                    {{ checkPersonnelLeft.length }}/{{ allPersonnelLeft.length }}
                </n-checkbox>
                <span class="panel-title">单位人员</span>
            </div>
            <n-input v-model:value="leftSearch" placeholder="请输入姓名/部门" clearable class="mb-8px" />
            <div class="panel-content">
                <n-tree
                    :data="processedTreeData"
                    :default-expand-all="leftSearch.length > 0"
                    :checked-keys="leftChecked"
                    :label-field="leftLabel"
                    :key-field="leftKey"
                    :render-label="renderLeftLabel"
                    :cascade="!isPaperDownload"
                    checkable
                    block-line
                    expand-on-click
                    :check-strategy="isPaperDownload ? 'child' : 'all'"
                    @update:checked-keys="updateLeftChecked"
                />
            </div>
        </div>
 
        <!-- 中间操作按钮 -->
        <div class="transfer-action">
            <n-button size="small" type="primary" @click="handleGrant" :disabled="!checkPersonnelLeft.length">
                发放 →
            </n-button>
            <n-button
                v-if="!props.hideRecycleButton"
                size="small"
                type="warning"
                @click="handleRecycle"
                :disabled="!rightChecked.length"
            >
                ← 回收
            </n-button>
        </div>

        <!-- 右侧已选人员列表（checkbox group） -->
        <div class="transfer-panel">
            <div class="panel-header">
                <n-checkbox :checked="isAllRightChecked" @update:checked="toggleRightCheckAll">
                    {{ rightChecked.length }}/{{ selectedList.filter((item) => !item.disabled).length }}
                </n-checkbox>
                <span class="panel-title">已选人员</span>
            </div>
            <n-input v-model:value="rightSearch" placeholder="请输入姓名" clearable class="mb-8px" />
            <div class="panel-content">
                <n-checkbox-group v-model:value="rightChecked">
                    <div v-for="item in filteredSelectedList" :key="item[rightKey]" class="checkbox-item">
                        <n-checkbox :value="item[rightKey]" :disabled="item.disabled">
                            <template #default>
                                <span>{{ item[rightLabel] }}</span>
                                <span v-if="item.status === 3" style="color: #005eff; margin-left: 2px; font-size: 10px"
                                    >（回收中）</span
                                >
                                <span v-if="item.status === 4" style="color: #005eff; margin-left: 2px; font-size: 10px"
                                    >（已回收）</span
                                >
                                <span v-if="item.status === 5" style="color: #005eff; margin-left: 2px; font-size: 10px"
                                    >（处置中）</span
                                >
                            </template>
                        </n-checkbox>
                    </div>
                </n-checkbox-group>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { ref, computed, h, watch, onMounted } from 'vue';
import { NTree, NButton, NInput, NCheckbox, NCheckboxGroup, TreeOption } from 'naive-ui';

/**
 * 组件props说明：
 * treeData: 组织架构树（含人员）
 * selectedIds: 已选人员id数组
 * approvingIds: 审批中人员id数组
 * selectedList: 已选人员详细信息（右侧列表渲染用，需含key、label）
 */
const props = defineProps<{
    selectedIds?: string[];
    approvingIds?: string[];
    leftKeyField?: string;
    leftLabelField?: string;
    rightKeyField?: string;
    rightLabelField?: string;
    params?: any;
    recordData?: any[];
    treeData?: any;
    hideRecycleButton?: boolean;
}>();

const emit = defineEmits<{
    (e: 'grant', persons: { key: string; label: string }[]): void;
    (e: 'recycle', keys: string[]): void;
}>();

// 自定义字段
const leftKey = computed(() => props.leftKeyField || 'key');
const leftLabel = computed(() => props.leftLabelField || 'label');
const rightKey = computed(() => props.rightKeyField || 'key');
const rightLabel = computed(() => props.rightLabelField || 'label');
const renderLeftLabel = ({ option }: { option: TreeOption }) => {
    const key = String(option[leftKey.value]);
    const baseLabel = option[leftLabel.value];
    let statusVNode: any = undefined;

    // 檢查是否為人員節點（沒有children屬性）
    const isPersonNode = !('children' in option);

    if (isPersonNode) {
        // 检查是否在当前权限类型下发放审批中
        if (grantingUserIds.value.has(key)) {
            statusVNode = h('span', { style: 'color: #005EFF; margin-left: 2px; font-size: 10px;' }, '（发放中）');
        }
        // 检查是否在当前权限类型下已发放
        else if (grantedUserIds.value.has(key)) {
            statusVNode = h('span', { style: 'color: #005EFF; margin-left: 2px; font-size: 10px;' }, '（已发放）');
        }
        // 检查是否在当前权限类型下已回收
        else if (recycledUserIds.value.has(key)) {
            statusVNode = h('span', { style: 'color: #005EFF; margin-left: 2px; font-size: 10px;' }, '（已回收）');
        }
        // 检查是否在当前权限类型下处置中
        else if (disposingUserIds.value.has(key)) {
            statusVNode = h('span', { style: 'color: #005EFF; margin-left: 2px; font-size: 10px;' }, '（处置中）');
        }
    }

    return h('span', null, statusVNode ? [baseLabel, statusVNode] : [baseLabel]);
};

// 这些集合目前不使用，但保留以备将来需要
// const approvingIdsSet = computed(() => new Set(props.approvingIds || []));
// const selectedIdsSet = computed(() => new Set(props.selectedIds || []));

/**
 * 获取所有节点 key
 * @param tree 组织树
 * @param type 'all' 返回全部节点key，'person' 仅返回没有children字段的人员key
 */
function getAllKeysLeft(tree: TreeOption[], type: 'all' | 'person' = 'all'): string[] {
    const keys: string[] = [];
    const dfs = (nodes: TreeOption[]) => {
        nodes.forEach((node) => {
            if (type === 'all') {
                keys.push(String(node[leftKey.value]));
            } else if (type === 'person' && !('children' in node)) {
                keys.push(String(node[leftKey.value]));
            }
            if (node.children && node.children.length) {
                dfs(node.children);
            }
        });
    };
    dfs(tree);
    return keys;
}

// 组织树数据
const internalTreeData = ref<TreeOption[]>([]);

// 根据 recordData 和 params 过滤已选人员
const selectedList = computed(() => {
    const filteredData = getFilteredRecordData();

    // 过滤掉 status === 1 的人员（发放审批中的人员）
    const statusFilteredData = filteredData.filter((item: any) => {
        return item.status !== 1;
    });

    const result = statusFilteredData.map((item: any) => ({
        key: item.userId || item.key,
        label: item.userNickName || item.nickname || item.label,
        value: item.userId || item.value,
        disabled: item.status === 3, // 回收审批中的人员禁用
        status: item.status, // 保存status用于自定义渲染
        ...item
    }));

    return result;
});

// 根据 params 过滤 recordData 的辅助函数
const getFilteredRecordData = () => {
    if (!props.recordData || !Array.isArray(props.recordData)) {
        return [];
    }

    return props.recordData.filter((item: any) => {
        // 如果 params 为空，则返回所有数据
        if (!props.params) {
            return true;
        }

        // 根据 params 中的条件进行过滤
        if (props.params.fileForm !== undefined && item.fileForm !== props.params.fileForm) {
            return false;
        }
        if (props.params.filePermission !== undefined && item.filePermission !== props.params.filePermission) {
            return false;
        }

        return true;
    });
};

// 获取当前权限类型下所有人员的 userId 集合（目前不使用，但保留以备将来需要）
// const allRecordUserIds = computed(() => {
//     const filteredData = getFilteredRecordData();
//     return new Set(filteredData.map((item) => item.userId));
// });

// 获取当前权限类型下发放审批中的人员ID集合（status === 1）
const grantingUserIds = computed(() => {
    const filteredData = getFilteredRecordData();
    return new Set(filteredData.filter((item) => item.status === 1).map((item) => item.userId));
});

// 获取当前权限类型下已发放的人员ID集合（status === 2）
const grantedUserIds = computed(() => {
    const filteredData = getFilteredRecordData();
    return new Set(filteredData.filter((item) => item.status === 2).map((item) => item.userId));
});

// 获取当前权限类型下已回收的人员ID集合（status === 4）
const recycledUserIds = computed(() => {
    const filteredData = getFilteredRecordData();
    return new Set(filteredData.filter((item) => item.status === 4).map((item) => item.userId));
});

// 获取当前权限类型下处置中的人员ID集合（status === 5）
const disposingUserIds = computed(() => {
    const filteredData = getFilteredRecordData();
    return new Set(filteredData.filter((item) => item.status === 5).map((item) => item.userId));
});

// 判断是否为纸质文件一次下载权限
const isPaperDownload = computed(() => {
    return props.params?.fileForm === 2 && props.params?.filePermission === 3;
});

// 判断右侧是否有人（用于纸质文件一次下载的特殊逻辑）
const hasAnyUsers = computed(() => {
    const filteredData = getFilteredRecordData();
    return filteredData.length > 0;
});

// 递归处理树，设置禁用
function markTreeDisabled(nodes: TreeOption[]): TreeOption[] {
    return nodes.map((node) => {
        let disabled = false;
        const key = String(node[leftKey.value]);

        // 特殊逻辑：纸质文件一次下载
        if (isPaperDownload.value) {
            // 如果右侧有人，则左侧所有人员都禁用
            if (hasAnyUsers.value) {
                disabled = true;
            }
        }

        // 检查当前用户是否在右侧数据中，且 status < 4
        const filteredData = getFilteredRecordData();
        const userRecord = filteredData.find((item: any) => item.userId === key);
        if (userRecord && userRecord.status < 4) {
            disabled = true;
        }

        const newNode: TreeOption = {
            ...node,
            disabled
        };
        if (node.children && node.children.length) {
            newNode.children = markTreeDisabled(node.children);
        }
        return newNode;
    });
}

const processedTreeData = computed(() => markTreeDisabled(internalTreeData.value));

// 根据搜索关键词过滤已选人员列表
const filteredSelectedList = computed(() => {
    if (!rightSearch.value) {
        return selectedList.value;
    }

    return selectedList.value.filter((item: any) => {
        const label = String(item[rightLabel.value] || '');
        return label.toLowerCase().includes(rightSearch.value.toLowerCase());
    });
});

/**
 * 左侧树相关
 */
const leftSearch = ref('');
const leftChecked = ref<string[]>([]);
const updateLeftChecked = (keys: string[]) => {
    // 如果是纸质文件一次下载（单选模式），只保留最后一个选中的
    if (isPaperDownload.value) {
        // 获取所有人员节点
        const allPersonnel = getAllKeysLeft(internalTreeData.value, 'person');
        // 过滤出人员节点（排除组织节点）
        const personnelKeys = keys.filter(key => allPersonnel.includes(key));
        // 只保留最后一个选中的人员
        leftChecked.value = personnelKeys.slice(-1);
    } else {
        leftChecked.value = keys;
    }
};
const allPersonnelLeft = computed(() => getAllKeysLeft(internalTreeData.value, 'person'));
const checkPersonnelLeft = computed(() => {
    return leftChecked.value.filter((k) => allPersonnelLeft.value.includes(k));
});
const isAllLeftChecked = computed(() => {
    return (
        leftChecked.value.length === getAllKeysLeft(internalTreeData.value).length &&
        getAllKeysLeft(internalTreeData.value).length > 0
    );
});
const toggleLeftCheckAll = (v: boolean) => {
    leftChecked.value = v ? getAllKeysLeft(internalTreeData.value) : [];
};

/**
 * 右侧列表相关
 */
const rightSearch = ref('');
const rightChecked = ref<string[]>([]);
const isAllRightChecked = computed(() => {
    const enabledItems = filteredSelectedList.value.filter((item) => !item.disabled);
    return enabledItems.length > 0 && rightChecked.value.length === enabledItems.length;
});
const toggleRightCheckAll = (v: boolean) => {
    if (v) {
        // 只选择未禁用的项目
        const enabledKeys = filteredSelectedList.value
            .filter((item) => !item.disabled)
            .map((item) => item[rightKey.value]);
        rightChecked.value = enabledKeys;
    } else {
        rightChecked.value = [];
    }
};

/**
 * 发放/回收 按钮
 */
function handleGrant() {
    // 找到左侧树新勾选的人员对象
    const allPersons: { key: string; label: string }[] = internalTreeData.value
        .flatMap(function getPersons(node): { key: string; label: string }[] {
            if (!node.children) {
                return [
                    {
                        key: String(node[leftKey.value]),
                        label: String(node[leftLabel.value])
                    }
                ];
            }
            return node.children.flatMap(getPersons);
        })
        .filter((person) => {
            // 只包含在左侧选中且未禁用的用户
            const node = findNodeByKey(internalTreeData.value, person.key);
            return checkPersonnelLeft.value.includes(person.key) && !node?.disabled;
        });

    // 只返回本次新选中的
    emit('grant', allPersons);
    leftChecked.value = [];
}
function handleRecycle() {
    emit('recycle', [...rightChecked.value]);
    rightChecked.value = [];
}

// 构建树形结构
function buildTreeData(orgData: any): any[] {
    const result: any[] = [];

    // 收集所有子节点
    const children: any[] = [];

    // 添加用户节点
    if (orgData.userInfo && Array.isArray(orgData.userInfo)) {
        orgData.userInfo.forEach((user: any) => {
            children.push({
                key: user.userId,
                label: user.nickname,
                value: user.userId,
                status: user.status // 保存status信息
            });
        });
    }

    // 处理子组织
    if (orgData.children && Array.isArray(orgData.children)) {
        orgData.children.forEach((child: any) => {
            const childOrgNode = buildTreeData(child)[0]; // 获取子组织的根节点
            children.push(childOrgNode);
        });
    }

    // 添加组织节点，只有在有子项时才添加 children 属性
    const orgNode: any = {
        key: orgData.orgId,
        label: orgData.orgName,
        value: orgData.orgId
    };

    // 只有在有子项时才添加 children 属性
    if (children.length > 0) {
        orgNode.children = children;
    }

    result.push(orgNode);
    return result;
}

// 根据 key 查找节点
function findNodeByKey(nodes: any[], key: string): any {
    for (const node of nodes) {
        if (node.key === key) {
            return node;
        }
        if (node.children && node.children.length > 0) {
            const found = findNodeByKey(node.children, key);
            if (found) {
                return found;
            }
        }
    }
    return null;
}

onMounted(() => {
    if (props.treeData) {
        internalTreeData.value = buildTreeData(props.treeData);
    }
});

// 监听 props.treeData 的变化
watch(
    () => props.treeData,
    (newTreeData) => {
        if (newTreeData) {
            internalTreeData.value = buildTreeData(newTreeData);
        }
    },
    { deep: true }
);
</script>

<style scoped lang="less">
.transfer-container {
    display: flex;
    gap: 8px;
    height: 260px;
    .transfer-panel {
        width: 200px;
        border: 1px solid #e0e0e6;
        padding: 12px;
        border-radius: 4px;
        display: flex;
        flex-direction: column;
        .panel-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;
        }
        .panel-title {
            font-weight: bold;
        }
        .panel-content {
            flex: 1;
            overflow-y: auto;
        }
        .checkbox-item {
            margin-bottom: 4px;
        }
    }
    .transfer-action {
        display: flex;
        flex-direction: column;
        justify-content: center;
        gap: 8px;
    }
}
</style>
