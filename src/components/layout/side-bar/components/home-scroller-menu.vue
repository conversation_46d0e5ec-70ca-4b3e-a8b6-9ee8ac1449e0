<template>
    <div class="home-scroller-bar">
        <div class="logo-area">
            <div class="logo-img-box">
                <img class="logo-img" :src="logo" />
            </div>
            <span class="logo-title">{{ base.title }}</span>
        </div>
        <div class="horizontal-menu-wrapper">
            <div class="arrow left" v-if="showLeftArrow" @click="scrollLeft">
                <svg-icon name="svgs-arrow-left" size="16" color="#000" />
            </div>
            <n-scrollbar ref="scrollbarRef" class="home-top-menu-scrollbar" :x-scrollable="true">
                <n-menu
                    mode="horizontal"
                    :value="activeMenu"
                    :options="menuOptions"
                    :default-value="activeMenu"
                    @update:value="handleMenuClick"
                    class="custom-horizontal-menu"
                />
                <div ref="indicatorRef" class="menu-indicator"></div>
            </n-scrollbar>
            <div class="arrow right" v-if="showRightArrow" @click="scrollRight">
                <svg-icon name="svgs-arrow-right" size="16" color="#000" />
            </div>
        </div>
        <job-switching class="job-switching" />
        <notice v-if="appConfig.actionBar.isShowNotice" class="notice-wrapper" />
        <div class="avatar-wrapper">
            <avatar />
        </div>
    </div>
</template>

<script lang="ts" setup>
import Avatar from '@/components/layout/avatar/index.vue';
import logo from '@/assets/images/logo.webp';
import SvgIcon from '@/components/layout/svg-icon/index.vue';
import { MenuMixedOption } from 'naive-ui/es/menu/src/interface';
import configHooks from '@/config/config-hooks';
import useStore from '@/store/modules/main';
import base from '@/config/base';
import useAppConfigStore from '@/store/modules/app-config';

const store = useStore();
const appConfig = useAppConfigStore();

const props = withDefaults(
    defineProps<{
        activeMenu?: string;
    }>(),
    {
        activeMenu: 'Entry'
    }
);
const emit = defineEmits(['update:activeMenu']);
const { activeMenu } = useVModels(props, emit);

const menuOptions: MenuMixedOption[] | any = store.routes
    .filter((v: any) => configHooks.layout.filterNav(v))
    .map((route: any) => ({
        key: route.name,
        label: route.meta?.title || route.name,
        info: route
    }));

/**
 * @function handleMenuClick
 * @description 处理菜单点击事件，更新激活菜单项
 * @param {string} key - 被点击菜单项的key
 */
const handleMenuClick = (key: string) => {
    activeMenu.value = key;
};

/**
 * 滚动菜单箭头
 */
const scrollbarRef = ref();
const indicatorRef = ref<HTMLElement | null>(null);
const showLeftArrow = ref(false);
const showRightArrow = ref(false);
let scrollContentEl: HTMLElement | null = null;
let menuItems: HTMLElement[] = [];

// 初始化菜单项和指示器位置
const initMenuItems = () => {
    nextTick(() => {
        // 通过 nextTick 确保DOM更新完成
        // 使用更可靠的方式查找菜单元素
        const scrollbarContainer =
            scrollbarRef.value?.scrollbarInstRef?.containerRef || scrollbarRef.value?.containerRef;
        const menuEl = scrollbarContainer?.querySelector('.n-menu');

        if (menuEl) {
            menuItems = Array.from(menuEl.querySelectorAll('.n-menu-item'));

            // 添加hover事件监听
            menuItems.forEach((item) => {
                const handleEnter = () => handleMenuItemHover(item);
                const handleLeave = () => handleMenuItemLeave();

                item.addEventListener('mouseenter', handleEnter);
                item.addEventListener('mouseleave', handleLeave);

                // 存储事件处理器以便后续清理
                (item as any)._handleEnter = handleEnter;
                (item as any)._handleLeave = handleLeave;
            });

            // 延迟更新指示器位置，确保样式已应用
            setTimeout(() => {
                updateIndicatorPosition();
            }, 100);
        } else {
            // 如果没有找到菜单，再次尝试
            setTimeout(initMenuItems, 200);
        }
    });
};

// 更新指示器位置
const updateIndicatorPosition = () => {
    if (!indicatorRef.value || menuItems.length === 0) {
        return;
    }

    // 查找激活的菜单项
    const activeItem = menuItems.find(
        (item) =>
            item.classList.contains('n-menu-item--selected') || item.querySelector('.n-menu-item-content--selected')
    );

    if (activeItem) {
        const menuContainer = activeItem.closest('.n-menu');
        if (menuContainer) {
            const containerRect = menuContainer.getBoundingClientRect();
            const itemRect = activeItem.getBoundingClientRect();

            const offsetLeft = itemRect.left - containerRect.left;
            const offsetWidth = itemRect.width;

            indicatorRef.value.style.transform = `translateX(${offsetLeft + offsetWidth * 0.1}px)`;
            indicatorRef.value.style.width = `${offsetWidth * 0.8}px`;
            indicatorRef.value.style.opacity = '1';
        }
    } else {
        // 如果没有找到激活项，隐藏指示器
        indicatorRef.value.style.opacity = '0';
    }
};

// 处理菜单项hover
const handleMenuItemHover = (item: HTMLElement) => {
    if (!indicatorRef.value) return;

    const menuContainer = item.closest('.n-menu');
    if (menuContainer) {
        const containerRect = menuContainer.getBoundingClientRect();
        const itemRect = item.getBoundingClientRect();

        const offsetLeft = itemRect.left - containerRect.left;
        const offsetWidth = itemRect.width;

        indicatorRef.value.style.transform = `translateX(${offsetLeft + offsetWidth * 0.1}px)`;
        indicatorRef.value.style.width = `${offsetWidth * 0.8}px`;
        indicatorRef.value.style.opacity = '1';
    }
};

// 处理菜单项leave
const handleMenuItemLeave = () => {
    updateIndicatorPosition();
};

// 更新箭头显示逻辑
const updateArrows = () => {
    if (!scrollContentEl) return;
    showLeftArrow.value = scrollContentEl.scrollLeft > 0;
    showRightArrow.value = scrollContentEl.scrollLeft + scrollContentEl.clientWidth < scrollContentEl.scrollWidth;
};

// 左右滚动
const scrollLeft = () => {
    if (scrollContentEl) {
        scrollContentEl.scrollBy({ left: -100, behavior: 'smooth' });
        setTimeout(updateArrows, 300);
    }
};
const scrollRight = () => {
    if (scrollContentEl) {
        scrollContentEl.scrollBy({ left: 100, behavior: 'smooth' });
        setTimeout(updateArrows, 300);
    }
};
// 初始化和事件监听
onMounted(() => {
    nextTick(() => {
        scrollContentEl = scrollbarRef.value?.scrollbarInstRef?.containerRef;
        if (scrollContentEl) {
            scrollContentEl.addEventListener('scroll', updateArrows);
        }
        updateArrows();
        initMenuItems();
    });
    window.addEventListener('resize', updateArrows);
});

// 监听菜单数据变化
watchEffect(() => {
    nextTick(() => {
        updateArrows();
        updateIndicatorPosition();
        setTimeout(updateArrows, 200);
    });
});

// 监听 activeMenu 变化
watch(
    activeMenu,
    () => {
        nextTick(() => {
            setTimeout(() => {
                updateIndicatorPosition();
            }, 100);
        });
    },
    { immediate: true }
);

onBeforeUnmount(() => {
    if (scrollContentEl) {
        scrollContentEl.removeEventListener('scroll', updateArrows);
    }
    window.removeEventListener('resize', updateArrows);

    // 清理菜单项事件监听器
    menuItems.forEach((item) => {
        if ((item as any)._handleEnter) {
            item.removeEventListener('mouseenter', (item as any)._handleEnter);
        }
        if ((item as any)._handleLeave) {
            item.removeEventListener('mouseleave', (item as any)._handleLeave);
        }
    });
});
</script>

<style scoped lang="less">
.home-scroller-bar {
    display: flex;
    align-items: center;
    height: var(--logo-height);
    background: #fff;
    border-bottom: 1px solid rgba(243, 244, 246, 1);
    padding: 0 20px;
    min-width: 1000px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.03);
    .logo-area {
        display: flex;
        align-items: center;
        margin-right: 40px;
        .logo-img-box {
            display: flex;
            justify-content: center;
            align-items: center;
            width: 50px;
            height: var(--logo-height);
            margin-right: 10px;
            .logo-img {
                width: 38px;
                height: 38px;
            }
        }
        .logo-title {
            font-size: 26px;
            font-weight: 900;
            color: #000;
            min-width: 200px;
            font-family: 'Source Han Sans', sans-serif;
            white-space: nowrap;
        }
    }
    .horizontal-menu-wrapper {
        position: relative;
        display: flex;
        align-items: center;
        height: 100%;
        padding: 0 50px;
        flex: 1 1 auto;
        min-width: 0;
        max-width: 100%;
        overflow: hidden;
        .arrow {
            width: 30px;
            height: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 20px;
            cursor: pointer;
            position: absolute;
            margin-bottom: 2px;

            &.left {
                left: 15px;
            }
            &.right {
                right: 15px;
            }
        }
        .home-top-menu-scrollbar {
            flex: 1 1 auto;
            min-width: 0;
            max-width: 100%;
            position: relative;
        }
    }
    .job-switching {
        margin-left: 16px;
    }
    .notice-wrapper {
        margin-left: 16px;
        color: #000;
    }
    .avatar-wrapper {
        padding-left: 16px;
        padding-right: 16px;
    }
}
:deep(.n-menu) {
    .n-menu-item {
        position: relative;
    }
}

.menu-indicator {
    position: absolute;
    bottom: 0;
    height: 4px;
    border-radius: 2px;
    background-color: #005eff;
    transition: all 0.3s ease-in-out;
    transform: translateX(0);
    width: 0;
    left: 0;
    z-index: 10;
    opacity: 0;
    pointer-events: none;
}
</style>
