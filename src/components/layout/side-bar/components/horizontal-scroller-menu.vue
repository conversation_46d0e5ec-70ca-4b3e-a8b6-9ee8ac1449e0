<template>
    <div class="horizontal-menu-wrapper">
        <div class="arrow left" v-if="showLeftArrow" @click="scrollLeft">
            <svg-icon name="svgs-arrow-left" size="16" color="#fff" />
        </div>
        <n-scrollbar
            ref="scrollbarRef"
            class="horizontal-scroller-menu"
            :class="{ 'zy-scroller-menu': layoutMode === 'ttb' }"
            :x-scrollable="true"
        >
            <n-menu
                mode="horizontal"
                :value="defaultPath"
                :options="menuOptions"
                :default-value="defaultPath"
                @update:value="onMenuClick"
            />
            <div ref="indicatorRef" class="menu-indicator"></div>
        </n-scrollbar>
        <div class="arrow right" v-if="showRightArrow" @click="scrollRight">
            <svg-icon name="svgs-arrow-right" size="16" color="#fff" />
        </div>
    </div>
</template>

<script lang="ts" setup>
import { h, ref, computed, onMounted, nextTick, watchEffect, onBeforeUnmount, watch } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import useStore from '@/store/modules/main';
import { NIcon } from 'naive-ui';
import SvgIcon from '@/components/layout/svg-icon/index.vue';
import { SystemRouteRow } from '@/typings';
import config from '@/config/config';
import configHooks from '@/config/config-hooks';
import useAppConfigStore from '@/store/modules/app-config';

const router = useRouter();
const route = useRoute();
const store = useStore();
const appConfig = useAppConfigStore();

const layoutMode = computed(() => {
    return appConfig.getLayoutMode;
});

const defaultPath = computed(() => route.meta.breadcrumbs?.[0].name) as unknown as string;
const menuOptions: any = store.routes
    .filter((v) => configHooks.layout.filterNav(v))
    .map((route) => ({
        key: route.name,
        label: route.meta?.title || route.name,
        info: route,
        icon:
            config.router.needSideMenuIcon && route?.meta?.icon
                ? () =>
                      h(NIcon, null, {
                          default: () =>
                              h(SvgIcon, {
                                  prefix: 'icon',
                                  name: route?.meta?.icon as string
                              })
                      })
                : null
    }));
const onMenuClick: any = (_key: string, row: SystemRouteRow) => {
    router.push({ name: row.info.name });
};

/**
 * 滚动菜单箭头和指示器
 */
const scrollbarRef = ref();
const indicatorRef = ref<HTMLElement | null>(null);
const showLeftArrow = ref(false);
const showRightArrow = ref(false);
let scrollContentEl: HTMLElement | null = null;
let menuItems: HTMLElement[] = [];

// 更新箭头显示逻辑
const updateArrows = () => {
    if (!scrollContentEl) return;
    showLeftArrow.value = scrollContentEl.scrollLeft > 0;
    showRightArrow.value = scrollContentEl.scrollLeft + scrollContentEl.clientWidth < scrollContentEl.scrollWidth;
};

// 初始化菜单项和指示器位置
const initMenuItems = () => {
    nextTick(() => {
        // 使用更可靠的方式查找菜单元素
        const scrollbarContainer =
            scrollbarRef.value?.scrollbarInstRef?.containerRef || scrollbarRef.value?.containerRef;
        const menuEl = scrollbarContainer?.querySelector('.n-menu');

        if (menuEl) {
            menuItems = Array.from(menuEl.querySelectorAll('.n-menu-item'));

            // 添加hover事件监听
            menuItems.forEach((item) => {
                const handleEnter = () => handleMenuItemHover(item);
                const handleLeave = () => handleMenuItemLeave();

                item.addEventListener('mouseenter', handleEnter);
                item.addEventListener('mouseleave', handleLeave);

                // 存储事件处理器以便后续清理
                (item as any)._handleEnter = handleEnter;
                (item as any)._handleLeave = handleLeave;
            });

            // 延迟更新指示器位置，确保样式已应用
            setTimeout(() => {
                updateIndicatorPosition();
            }, 100);
        } else {
            // 如果没有找到菜单，再次尝试
            setTimeout(initMenuItems, 200);
        }
    });
};

// 更新指示器位置
const updateIndicatorPosition = () => {
    if (!indicatorRef.value || menuItems.length === 0) {
        return;
    }

    // 查找激活的菜单项
    const activeItem = menuItems.find(
        (item) =>
            item.classList.contains('n-menu-item--selected') || item.querySelector('.n-menu-item-content--selected')
    );

    if (activeItem) {
        const menuContainer = activeItem.closest('.n-menu');
        if (menuContainer) {
            const containerRect = menuContainer.getBoundingClientRect();
            const itemRect = activeItem.getBoundingClientRect();

            const offsetLeft = itemRect.left - containerRect.left;
            const offsetWidth = itemRect.width;

            indicatorRef.value.style.transform = `translateX(${offsetLeft + offsetWidth * 0.1}px)`;
            indicatorRef.value.style.width = `${offsetWidth * 0.8}px`;
            indicatorRef.value.style.opacity = '1';
        }
    } else {
        // 如果没有找到激活项，隐藏指示器
        indicatorRef.value.style.opacity = '0';
    }
};

// 处理菜单项hover
const handleMenuItemHover = (item: HTMLElement) => {
    if (!indicatorRef.value) return;

    const menuContainer = item.closest('.n-menu');
    if (menuContainer) {
        const containerRect = menuContainer.getBoundingClientRect();
        const itemRect = item.getBoundingClientRect();

        const offsetLeft = itemRect.left - containerRect.left;
        const offsetWidth = itemRect.width;

        indicatorRef.value.style.transform = `translateX(${offsetLeft + offsetWidth * 0.1}px)`;
        indicatorRef.value.style.width = `${offsetWidth * 0.8}px`;
        indicatorRef.value.style.opacity = '1';
    }
};

// 处理菜单项leave
const handleMenuItemLeave = () => {
    updateIndicatorPosition();
};

// 左右滚动
const scrollLeft = () => {
    if (scrollContentEl) {
        scrollContentEl.scrollBy({ left: -100, behavior: 'smooth' });
        setTimeout(updateArrows, 300);
    }
};
const scrollRight = () => {
    if (scrollContentEl) {
        scrollContentEl.scrollBy({ left: 100, behavior: 'smooth' });
        setTimeout(updateArrows, 300);
    }
};

// 初始化和事件监听
onMounted(() => {
    nextTick(() => {
        scrollContentEl = scrollbarRef.value?.scrollbarInstRef?.containerRef;
        if (scrollContentEl) {
            scrollContentEl.addEventListener('scroll', updateArrows);
        }
        updateArrows();
        initMenuItems();
    });
    window.addEventListener('resize', updateArrows);
});

// 监听菜单数据变化
watchEffect(() => {
    nextTick(() => {
        updateArrows();
        updateIndicatorPosition();
        setTimeout(updateArrows, 200);
    });
});

// 监听路由变化
watch(
    () => route.name,
    () => {
        nextTick(() => {
            setTimeout(() => {
                updateIndicatorPosition();
            }, 100);
        });
    },
    { immediate: true }
);

onBeforeUnmount(() => {
    if (scrollContentEl) {
        scrollContentEl.removeEventListener('scroll', updateArrows);
    }
    window.removeEventListener('resize', updateArrows);

    // 清理菜单项事件监听器
    menuItems.forEach((item) => {
        if ((item as any)._handleEnter) {
            item.removeEventListener('mouseenter', (item as any)._handleEnter);
        }
        if ((item as any)._handleLeave) {
            item.removeEventListener('mouseleave', (item as any)._handleLeave);
        }
    });
});
</script>

<style lang="less" scoped>
.horizontal-menu-wrapper {
    position: relative;
    display: flex;
    align-items: center;
    height: 100%;
    padding: 0 30px;

    .arrow {
        width: 30px;
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 20px;
        cursor: pointer;
        position: absolute;

        &.left {
            left: 0;
        }
        &.right {
            right: 0;
        }
    }

    .horizontal-scroller-menu {
        position: relative;
    }
}

:deep(.n-menu) {
    .n-menu-item {
        position: relative;
    }
}

.menu-indicator {
    position: absolute;
    bottom: 0;
    height: 4px;
    border-radius: 2px;
    background: linear-gradient(180deg, rgba(156, 227, 255, 0.6) 0%, rgb(1, 79, 248) 110%);
    transition: all 0.3s ease-in-out;
    transform: translateX(0);
    width: 0;
    left: 0;
    z-index: 10;
    opacity: 0;
    pointer-events: none;
}
</style>
